{"format_version": "1.21.90", "minecraft:block": {"description": {"identifier": "ditsh:chair", "menu_category": {"category": "construction"}, "traits": {"minecraft:placement_direction": {"enabled_states": ["minecraft:cardinal_direction"]}}}, "components": {"minecraft:collision_box": {"origin": [-8, 0, -8], "size": [16, 8, 16]}, "minecraft:selection_box": true, "minecraft:geometry": "geometry.ithiefi_ditsh_chair", "minecraft:material_instances": {"*": {"texture": "chair", "render_method": "opaque"}}, "minecraft:destructible_by_mining": {"seconds_to_destroy": 1.5}, "minecraft:friction": 0.35, "minecraft:map_color": "#8B4513", "minecraft:item_visual": {"geometry": "geometry.ithiefi_ditsh_chair", "material_instances": {"*": {"texture": "chair", "render_method": "opaque", "isotropic": true}}}}, "permutations": [{"condition": "query.block_state('minecraft:cardinal_direction') == 'north'", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'south'", "components": {"minecraft:transformation": {"rotation": [0, 0, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'west'", "components": {"minecraft:transformation": {"rotation": [0, -90, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'east'", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}}}]}}