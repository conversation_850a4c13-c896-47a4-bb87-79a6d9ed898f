import { world, system } from "@minecraft/server";
world.afterEvents.playerPlaceBlock.subscribe((eventData) => {
    const block = eventData.block;
    if (block.hasTag('ditsh:stair')) {
        updateNeighborStairs(block);
    }
});
world.beforeEvents.playerBreakBlock.subscribe((eventData) => {
    const block = eventData.block;
    system.run(() => {
        updateDestroyedStair(block);
    });
});
function updateDestroyedStair(block) {
    updateStair(block);
    const neighbors = [
        block.north(),
        block.south(),
        block.east(),
        block.west()
    ];
    for (const neighbor of neighbors) {
        if (neighbor && neighbor.hasTag("ditsh:stair")) {
            updateStair(neighbor);
        }
    }
}
function updateNeighborStairs(block) {
    updateStair(block);
    const blockHalf = block.permutation.getState("minecraft:vertical_half");
    const neighbors = [
        block.north(),
        block.south(),
        block.east(),
        block.west()
    ];
    for (const neighbor of neighbors) {
        if (neighbor &&
            neighbor.permutation.getState("minecraft:vertical_half") === blockHalf &&
            neighbor.hasTag("ditsh:stair")) {
            updateStair(neighbor);
        }
    }
}
function updateStair(block) {
    const northBlock = block.north();
    const southBlock = block.south();
    const eastBlock = block.east();
    const westBlock = block.west();
    const blockChange = block.permutation;
    const stairHalf = block.permutation.getState("minecraft:vertical_half");
    const northHalf = northBlock?.permutation.getState("minecraft:vertical_half");
    const southHalf = southBlock?.permutation.getState("minecraft:vertical_half");
    const eastHalf = eastBlock?.permutation.getState("minecraft:vertical_half");
    const westHalf = westBlock?.permutation.getState("minecraft:vertical_half");
    if (block.hasTag("ditsh:north")) {
        if ((!(northHalf === stairHalf || southHalf === stairHalf))
            || (westHalf === stairHalf && westBlock?.hasTag("ditsh:north") &&
                northHalf === stairHalf && northBlock?.hasTag("ditsh:east"))
            || (eastHalf === stairHalf && eastBlock?.hasTag("ditsh:north") &&
                northHalf === stairHalf && northBlock?.hasTag("ditsh:west"))) {
            block.setPermutation(blockChange.withState('ditsh:type', 1));
        }
        else if (northHalf === stairHalf && northBlock?.hasTag("ditsh:west")) {
            block.setPermutation(blockChange.withState('ditsh:type', 4));
        }
        else if (northHalf === stairHalf && northBlock?.hasTag("ditsh:east")) {
            block.setPermutation(blockChange.withState('ditsh:type', 5));
        }
        else if (southHalf === stairHalf && southBlock?.hasTag("ditsh:west")) {
            block.setPermutation(blockChange.withState('ditsh:type', 2));
        }
        else if (southHalf === stairHalf && southBlock?.hasTag("ditsh:east")) {
            block.setPermutation(blockChange.withState('ditsh:type', 3));
        }
    }
    if (block.hasTag("ditsh:south")) {
        if ((!(northHalf === stairHalf || southHalf === stairHalf))
            || (eastHalf === stairHalf && eastBlock?.hasTag("ditsh:south") &&
                southHalf === stairHalf && southBlock?.hasTag("ditsh:west"))
            || (westHalf === stairHalf && westBlock?.hasTag("ditsh:south") &&
                southHalf === stairHalf && southBlock?.hasTag("ditsh:east"))) {
            block.setPermutation(blockChange.withState('ditsh:type', 1));
        }
        else if (northHalf === stairHalf && northBlock?.hasTag("ditsh:west")) {
            block.setPermutation(blockChange.withState('ditsh:type', 3));
        }
        else if (northHalf === stairHalf && northBlock?.hasTag("ditsh:east")) {
            block.setPermutation(blockChange.withState('ditsh:type', 2));
        }
        else if (southHalf === stairHalf && southBlock?.hasTag("ditsh:west")) {
            block.setPermutation(blockChange.withState('ditsh:type', 4));
        }
        else if (southHalf === stairHalf && southBlock?.hasTag("ditsh:east")) {
            block.setPermutation(blockChange.withState('ditsh:type', 5));
        }
    }
    if (block.hasTag("ditsh:west")) {
        if ((!(westHalf === stairHalf || eastHalf === stairHalf))
            || (southHalf === stairHalf && southBlock?.hasTag("ditsh:west") &&
                westHalf === stairHalf && westBlock?.hasTag("ditsh:north"))
            || (northHalf === stairHalf && northBlock?.hasTag("ditsh:west") &&
                westHalf === stairHalf && westBlock?.hasTag("ditsh:south"))) {
            block.setPermutation(blockChange.withState('ditsh:type', 1));
        }
        else if (westHalf === stairHalf && westBlock?.hasTag("ditsh:north")) {
            block.setPermutation(blockChange.withState('ditsh:type', 5));
        }
        else if (westHalf === stairHalf && westBlock?.hasTag("ditsh:south")) {
            block.setPermutation(blockChange.withState('ditsh:type', 4));
        }
        else if (eastHalf === stairHalf && eastBlock?.hasTag("ditsh:north")) {
            block.setPermutation(blockChange.withState('ditsh:type', 3));
        }
        else if (eastHalf === stairHalf && eastBlock?.hasTag("ditsh:south")) {
            block.setPermutation(blockChange.withState('ditsh:type', 2));
        }
    }
    if (block.hasTag("ditsh:east")) {
        if ((!(westHalf === stairHalf || eastHalf === stairHalf))
            || (northHalf === stairHalf && northBlock?.hasTag("ditsh:east") &&
                eastHalf === stairHalf && eastBlock?.hasTag("ditsh:south"))
            || (southHalf === stairHalf && southBlock?.hasTag("ditsh:east") &&
                eastHalf === stairHalf && eastBlock?.hasTag('ditsh:north'))) {
            block.setPermutation(blockChange.withState('ditsh:type', 1));
        }
        else if (westHalf === stairHalf && westBlock?.hasTag("ditsh:north")) {
            block.setPermutation(blockChange.withState('ditsh:type', 2));
        }
        else if (westHalf === stairHalf && westBlock?.hasTag("ditsh:south")) {
            block.setPermutation(blockChange.withState('ditsh:type', 3));
        }
        else if (eastHalf === stairHalf && eastBlock?.hasTag("ditsh:north")) {
            block.setPermutation(blockChange.withState('ditsh:type', 5));
        }
        else if (eastHalf === stairHalf && eastBlock?.hasTag("ditsh:south")) {
            block.setPermutation(blockChange.withState('ditsh:type', 4));
        }
    }
}
