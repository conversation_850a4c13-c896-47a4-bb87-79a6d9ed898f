import { world, system } from "@minecraft/server";
world.afterEvents.playerPlaceBlock.subscribe((eventData) => {
    const block = eventData.block;
    if (block.hasTag('tap:stair')) {
        updateNeighborStairs(block);
    }
});
world.beforeEvents.playerBreakBlock.subscribe((eventData) => {
    const block = eventData.block;
    system.run(() => {
        updateDestroyedStair(block);
    });
});
function updateDestroyedStair(block) {
    updateStair(block);
    const neighbors = [
        block.north(),
        block.south(),
        block.east(),
        block.west()
    ];
    for (const neighbor of neighbors) {
        if (neighbor.hasTag("tap:stair")) {
            updateStair(neighbor);
        }
    }
}
function updateNeighborStairs(block) {
    updateStair(block);
    const blockHalf = block.permutation.getState("minecraft:vertical_half");
    const neighbors = [
        block.north(),
        block.south(),
        block.east(),
        block.west()
    ];
    for (const neighbor of neighbors) {
        if (neighbor.permutation.getState("minecraft:vertical_half") === blockHalf &&
            neighbor.hasTag("tap:stair")) {
            updateStair(neighbor);
        }
    }
}
function updateStair(block) {
    const northBlock = block.north();
    const southBlock = block.south();
    const eastBlock = block.east();
    const westBlock = block.west();
    const blockChange = block.permutation;
    const stairHalf = block.permutation.getState("minecraft:vertical_half");
    const northHalf = northBlock.permutation.getState("minecraft:vertical_half");
    const southHalf = southBlock.permutation.getState("minecraft:vertical_half");
    const eastHalf = eastBlock.permutation.getState("minecraft:vertical_half");
    const westHalf = westBlock.permutation.getState("minecraft:vertical_half");
    if (block.hasTag("tap:north")) {
        if ((!(northHalf === stairHalf || southHalf === stairHalf))
            || (westHalf === stairHalf && westBlock.hasTag("tap:north") &&
                northHalf === stairHalf && northBlock.hasTag("tap:east"))
            || (eastHalf === stairHalf && eastBlock.hasTag("tap:north") &&
                northHalf === stairHalf && northBlock.hasTag("tap:west"))) {
            block.setPermutation(blockChange.withState('tap:type', 1));
        }
        else if (northHalf === stairHalf && northBlock.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type', 4));
        }
        else if (northHalf === stairHalf && northBlock.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type', 5));
        }
        else if (southHalf === stairHalf && southBlock.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type', 2));
        }
        else if (southHalf === stairHalf && southBlock.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type', 3));
        }
    }
    if (block.hasTag("tap:south")) {
        if ((!(northHalf === stairHalf || southHalf === stairHalf))
            || (eastHalf === stairHalf && eastBlock.hasTag("tap:south") &&
                southHalf === stairHalf && southBlock.hasTag("tap:west"))
            || (westHalf === stairHalf && westBlock.hasTag("tap:south") &&
                southHalf === stairHalf && southBlock.hasTag("tap:east"))) {
            block.setPermutation(blockChange.withState('tap:type', 1));
        }
        else if (northHalf === stairHalf && northBlock.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type', 3));
        }
        else if (northHalf === stairHalf && northBlock.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type', 2));
        }
        else if (southHalf === stairHalf && southBlock.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type', 4));
        }
        else if (southHalf === stairHalf && southBlock.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type', 5));
        }
    }
    if (block.hasTag("tap:west")) {
        if ((!(westHalf === stairHalf || eastHalf === stairHalf))
            || (southHalf === stairHalf && southBlock.hasTag("tap:west") &&
                westHalf === stairHalf && westBlock.hasTag("tap:north"))
            || (northHalf === stairHalf && northBlock.hasTag("tap:west") &&
                westHalf === stairHalf && westBlock.hasTag("tap:south"))) {
            block.setPermutation(blockChange.withState('tap:type', 1));
        }
        else if (westHalf === stairHalf && westBlock.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type', 5));
        }
        else if (westHalf === stairHalf && westBlock.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type', 4));
        }
        else if (eastHalf === stairHalf && eastBlock.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type', 3));
        }
        else if (eastHalf === stairHalf && eastBlock.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type', 2));
        }
    }
    if (block.hasTag("tap:east")) {
        if ((!(westHalf === stairHalf || eastHalf === stairHalf))
            || (northHalf === stairHalf && northBlock.hasTag("tap:east") &&
                eastHalf === stairHalf && eastBlock.hasTag("tap:south"))
            || (southHalf === stairHalf && southBlock.hasTag("tap:east") &&
                eastHalf === stairHalf && eastBlock.hasTag('tap:north'))) {
            block.setPermutation(blockChange.withState('tap:type', 1));
        }
        else if (westHalf === stairHalf && westBlock.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type', 2));
        }
        else if (westHalf === stairHalf && westBlock.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type', 3));
        }
        else if (eastHalf === stairHalf && eastBlock.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type', 5));
        }
        else if (eastHalf === stairHalf && eastBlock.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type', 4));
        }
    }
}
