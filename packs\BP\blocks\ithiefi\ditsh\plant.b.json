{"format_version": "1.21.90", "minecraft:block": {"description": {"identifier": "ditsh:plant", "menu_category": {"category": "nature"}, "traits": {"minecraft:placement_direction": {"enabled_states": ["minecraft:cardinal_direction"]}}}, "components": {"minecraft:collision_box": {"origin": [-4, 0, -3.5], "size": [8, 12, 7]}, "minecraft:selection_box": {"origin": [-4, 0, -3.5], "size": [8, 12, 7]}, "minecraft:geometry": "geometry.ithiefi_ditsh_plant", "minecraft:material_instances": {"*": {"texture": "ditsh_plant", "render_method": "alpha_test"}}, "minecraft:destructible_by_mining": {"seconds_to_destroy": 1.0}, "minecraft:friction": 0.35, "minecraft:map_color": "#228B22", "minecraft:item_visual": {"geometry": "geometry.ithiefi_ditsh_plant", "material_instances": {"*": {"texture": "ditsh_plant", "render_method": "opaque", "isotropic": true}}}}, "permutations": [{"condition": "query.block_state('minecraft:cardinal_direction') == 'north'", "components": {"minecraft:transformation": {"rotation": [0, 180, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'south'", "components": {"minecraft:transformation": {"rotation": [0, 0, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'west'", "components": {"minecraft:transformation": {"rotation": [0, -90, 0]}}}, {"condition": "query.block_state('minecraft:cardinal_direction') == 'east'", "components": {"minecraft:transformation": {"rotation": [0, 90, 0]}}}]}}