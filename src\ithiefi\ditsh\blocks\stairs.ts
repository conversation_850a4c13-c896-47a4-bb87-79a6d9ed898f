/**
 * @fileoverview DitSH Custom Stairs Block Logic
 *
 * Handles automatic stair connection and placement logic for custom stairs in the DitSH add-on.
 * This system provides vanilla-like stair behavior including corner connections and proper orientation.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */

import { world, system, BlockPermutation, Block } from "@minecraft/server";

/**
 * Type definition for vertical half states
 */
type VerticalHalf = "top" | "bottom";

/**
 * Type definition for cardinal direction states
 */
type CardinalDirection = "north" | "south" | "east" | "west";

/**
 * Type definition for stair connection types
 */
type StairType = 1 | 2 | 3 | 4 | 5;

/**
 * Event handler for when a player places a block.
 * Automatically updates stair connections when a stair block is placed.
 */
world.afterEvents.playerPlaceBlock.subscribe((eventData) => {
    const block: Block = eventData.block;
    if (block.hasTag('tap:stair')) {
        updateNeighborStairs(block);
    }
});

/**
 * Event handler for when a player breaks a block.
 * Updates neighboring stairs when a stair block is destroyed.
 */
world.beforeEvents.playerBreakBlock.subscribe((eventData) => {
    const block: Block = eventData.block;
    system.run(() => {
        updateDestroyedStair(block);
    });
});

/**
 * Updates stairs when a stair block is destroyed.
 * This function handles the cleanup of stair connections when a block is removed.
 *
 * @param block - The block that was destroyed
 */
function updateDestroyedStair(block: Block): void {
    updateStair(block);

    const neighbors: (Block | undefined)[] = [
        block.north(),
        block.south(),
        block.east(),
        block.west()
    ];

    for (const neighbor of neighbors) {
        if (neighbor && neighbor.hasTag("tap:stair")) {
            updateStair(neighbor);
        }
    }
}

/**
 * Updates neighboring stairs when a new stair is placed.
 * This function ensures proper stair connections are maintained.
 *
 * @param block - The newly placed stair block
 */
function updateNeighborStairs(block: Block): void {
    updateStair(block);

    const blockHalf: string | number | boolean | undefined = block.permutation.getState("minecraft:vertical_half");

    const neighbors: (Block | undefined)[] = [
        block.north(),
        block.south(),
        block.east(),
        block.west()
    ];

    for (const neighbor of neighbors) {
        if (neighbor &&
            neighbor.permutation.getState("minecraft:vertical_half") === blockHalf &&
            neighbor.hasTag("tap:stair")) {
            updateStair(neighbor);
        }
    }
}

/**
 * Updates the stair connection type based on neighboring stairs.
 * This function analyzes adjacent blocks and sets the appropriate stair type for proper connections.
 *
 * @param block - The stair block to update
 */
function updateStair(block: Block): void {
    const northBlock: Block | undefined = block.north();
    const southBlock: Block | undefined = block.south();
    const eastBlock: Block | undefined = block.east();
    const westBlock: Block | undefined = block.west();
    const blockChange: BlockPermutation = block.permutation;

    const stairHalf: string | number | boolean | undefined = block.permutation.getState("minecraft:vertical_half");

    const northHalf: string | number | boolean | undefined = northBlock?.permutation.getState("minecraft:vertical_half");
    const southHalf: string | number | boolean | undefined = southBlock?.permutation.getState("minecraft:vertical_half");
    const eastHalf: string | number | boolean | undefined = eastBlock?.permutation.getState("minecraft:vertical_half");
    const westHalf: string | number | boolean | undefined = westBlock?.permutation.getState("minecraft:vertical_half");

    if (block.hasTag("tap:north")) {
        if ((!(northHalf === stairHalf || southHalf === stairHalf))
        || (westHalf === stairHalf && westBlock?.hasTag("tap:north") &&
            northHalf === stairHalf && northBlock?.hasTag("tap:east"))
        || (eastHalf === stairHalf && eastBlock?.hasTag("tap:north") &&
            northHalf === stairHalf && northBlock?.hasTag("tap:west"))
        ) {
            block.setPermutation(blockChange.withState('tap:type' as any, 1));
        } else if (northHalf === stairHalf && northBlock?.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 4));
        } else if (northHalf === stairHalf && northBlock?.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 5));
        } else if (southHalf === stairHalf && southBlock?.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 2));
        } else if (southHalf === stairHalf && southBlock?.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 3));
        }
    }
    if (block.hasTag("tap:south")) {
        if ((!(northHalf === stairHalf || southHalf === stairHalf))
        || (eastHalf === stairHalf && eastBlock?.hasTag("tap:south") &&
            southHalf === stairHalf && southBlock?.hasTag("tap:west"))
        || (westHalf === stairHalf && westBlock?.hasTag("tap:south") &&
            southHalf === stairHalf && southBlock?.hasTag("tap:east"))
        ) {
            block.setPermutation(blockChange.withState('tap:type' as any, 1));
        } else if (northHalf === stairHalf && northBlock?.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 3));
        } else if (northHalf === stairHalf && northBlock?.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 2));
        } else if (southHalf === stairHalf && southBlock?.hasTag("tap:west")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 4));
        } else if (southHalf === stairHalf && southBlock?.hasTag("tap:east")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 5));
        }
    }
    if (block.hasTag("tap:west")) {
        if ((!(westHalf === stairHalf || eastHalf === stairHalf))
        || (southHalf === stairHalf && southBlock?.hasTag("tap:west") &&
            westHalf === stairHalf && westBlock?.hasTag("tap:north"))
        || (northHalf === stairHalf && northBlock?.hasTag("tap:west") &&
            westHalf === stairHalf && westBlock?.hasTag("tap:south"))
        ) {
            block.setPermutation(blockChange.withState('tap:type' as any, 1));
        } else if (westHalf === stairHalf && westBlock?.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 5));
        } else if (westHalf === stairHalf && westBlock?.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 4));
        } else if (eastHalf === stairHalf && eastBlock?.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 3));
        } else if (eastHalf === stairHalf && eastBlock?.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 2));
        }
    }
    if (block.hasTag("tap:east")) {
        if ((!(westHalf === stairHalf || eastHalf === stairHalf))
        || (northHalf === stairHalf && northBlock?.hasTag("tap:east") &&
            eastHalf === stairHalf && eastBlock?.hasTag("tap:south"))
        || (southHalf === stairHalf && southBlock?.hasTag("tap:east") &&
            eastHalf === stairHalf && eastBlock?.hasTag('tap:north'))
        ) {
            block.setPermutation(blockChange.withState('tap:type' as any, 1));
        } else if (westHalf === stairHalf && westBlock?.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 2));
        } else if (westHalf === stairHalf && westBlock?.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 3));
        } else if (eastHalf === stairHalf && eastBlock?.hasTag("tap:north")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 5));
        } else if (eastHalf === stairHalf && eastBlock?.hasTag("tap:south")) {
            block.setPermutation(blockChange.withState('tap:type' as any, 4));
        }
    }
}